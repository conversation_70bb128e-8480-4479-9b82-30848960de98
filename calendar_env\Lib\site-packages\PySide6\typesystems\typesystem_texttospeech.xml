<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2017 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtTextToSpeech"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
  <load-typesystem name="typesystem_core.xml" generate="no"/>

  <object-type name="QTextToSpeech">
      <enum-type name="State"/>
      <enum-type name="BoundaryHint"/>
      <enum-type name="ErrorReason"/>
      <enum-type name="Capability" flags="Capabilities" since="6.6"/>
  </object-type>
  <object-type name="QTextToSpeechEngine"/>
  <value-type name="QVoice">
      <enum-type name="Gender"/>
      <enum-type name="Age"/>
  </value-type>
</typesystem>

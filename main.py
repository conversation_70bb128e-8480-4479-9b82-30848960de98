#!/usr/bin/env python3
"""
Calendar Desktop Application
A PySide6-based calendar application with task management
"""

import sys
import calendar
from datetime import datetime, date
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGridLayout, QLabel, QPushButton, QFrame, QScrollArea, QSplitter
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont, QPalette, QIcon


class CalendarWidget(QWidget):
    """Custom calendar widget"""
    date_clicked = Signal(QDate)

    def __init__(self):
        super().__init__()
        self.current_date = QDate.currentDate()
        self.selected_date = self.current_date
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setSpacing(0)
        layout.setContentsMargins(0, 0, 40, 0)  # Right margin for spacing from divider

        # Header with navigation
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 20)

        self.month_label = QLabel()
        self.month_label.setAlignment(Qt.AlignLeft)
        font = QFont()
        font.setPointSize(18)
        font.setBold(True)
        self.month_label.setFont(font)
        self.month_label.setStyleSheet("color: #111827; margin: 0; padding: 0;")

        # Navigation buttons
        self.prev_button = QPushButton("‹")
        self.prev_button.setFixedSize(24, 24)
        self.prev_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                font-size: 16px;
                color: #9CA3AF;
                border-radius: 4px;
            }
            QPushButton:hover {
                color: #6B7280;
            }
        """)
        self.prev_button.clicked.connect(self.prev_month)

        self.next_button = QPushButton("›")
        self.next_button.setFixedSize(24, 24)
        self.next_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                font-size: 16px;
                color: #9CA3AF;
                border-radius: 4px;
            }
            QPushButton:hover {
                color: #6B7280;
            }
        """)
        self.next_button.clicked.connect(self.next_month)

        header_layout.addWidget(self.month_label, 1)
        header_layout.addWidget(self.prev_button)
        header_layout.addWidget(self.next_button)

        # Week day headers
        weekday_layout = QGridLayout()
        weekday_layout.setSpacing(0)
        weekday_layout.setContentsMargins(0, 0, 0, 0)

        weekdays = ['M', 'T', 'W', 'T', 'F', 'S', 'S']
        for i, day in enumerate(weekdays):
            label = QLabel(day)
            label.setAlignment(Qt.AlignCenter)
            label.setFixedHeight(30)
            label.setStyleSheet("""
                QLabel {
                    color: #6B7280;
                    font-size: 12px;
                    font-weight: 500;
                    padding: 8px 0px;
                }
            """)
            weekday_layout.addWidget(label, 0, i)

        # Calendar grid
        self.calendar_frame = QFrame()
        self.calendar_layout = QGridLayout(self.calendar_frame)
        self.calendar_layout.setSpacing(0)
        self.calendar_layout.setContentsMargins(0, 0, 0, 0)

        layout.addLayout(header_layout)
        layout.addLayout(weekday_layout)
        layout.addWidget(self.calendar_frame)
        self.setLayout(layout)

        self.update_calendar()
        
    def update_calendar(self):
        # Clear existing date buttons
        for i in range(1, 7):  # rows 1-6 (row 0 is weekday headers)
            for j in range(7):  # columns 0-6
                item = self.calendar_layout.itemAtPosition(i, j)
                if item:
                    widget = item.widget()
                    if widget:
                        widget.deleteLater()
        
        # Update month label
        month_name = self.current_date.toString("MMMM yyyy")
        self.month_label.setText(month_name)
        
        # Get calendar data
        year = self.current_date.year()
        month = self.current_date.month()
        
        # Get first day of month and number of days
        first_day = QDate(year, month, 1)
        days_in_month = first_day.daysInMonth()
        
        # Get the weekday of the first day (0=Monday, 6=Sunday)
        first_weekday = first_day.dayOfWeek() - 1
        
        # Calculate previous month days to show
        prev_month_date = first_day.addMonths(-1)
        prev_month_days = prev_month_date.daysInMonth()
        
        row = 1
        col = 0
        
        # Add previous month's trailing days
        for i in range(first_weekday):
            day = prev_month_days - first_weekday + i + 1
            is_first_week = row == 0
            btn = self.create_date_button(day, True, prev_month_date.year(), prev_month_date.month(), is_first_week)
            self.calendar_layout.addWidget(btn, row, col)
            col += 1
        
        # Add current month days
        for day in range(1, days_in_month + 1):
            is_first_week = row == 0
            btn = self.create_date_button(day, False, year, month, is_first_week)
            self.calendar_layout.addWidget(btn, row, col)
            col += 1
            if col > 6:
                col = 0
                row += 1

        # Add next month's leading days
        next_month_date = first_day.addMonths(1)
        day = 1
        while row < 6:  # Limit to 6 rows total
            while col < 7:
                is_first_week = row == 0
                btn = self.create_date_button(day, True, next_month_date.year(), next_month_date.month(), is_first_week)
                self.calendar_layout.addWidget(btn, row, col)
                day += 1
                col += 1
            col = 0
            row += 1
            if row >= 6:
                break
    
    def create_date_button(self, day, is_other_month, year, month, is_first_week=False):
        # Create a container cell
        date_container = QFrame()
        date_container.setFixedSize(60, 50)  # Larger cells for better spacing

        # Create layout for the container
        container_layout = QVBoxLayout(date_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setAlignment(Qt.AlignCenter)

        # Create the date button
        date_button = QPushButton(str(day))
        date_button.setFixedSize(36, 36)  # Slightly larger than original

        # Create QDate for this button
        btn_date = QDate(year, month, day)

        # Determine states
        is_today = btn_date == QDate.currentDate()
        is_selected = day == 21 and not is_other_month  # Demo: 21st is selected

        # Style the container
        date_container.setStyleSheet("""
            QFrame {
                background-color: transparent;
                border: none;
            }
        """)

        # Style the button based on state
        if is_selected:
            # Selected: black background, white text
            date_button.setStyleSheet("""
                QPushButton {
                    background-color: #111827;
                    color: white;
                    border: none;
                    border-radius: 18px;
                    font-weight: 600;
                    font-size: 14px;
                }
            """)
        elif is_today:
            # Today: blue text, bold
            date_button.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    color: #3B82F6;
                    border: none;
                    border-radius: 18px;
                    font-weight: 600;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #F3F4F6;
                }
            """)
        elif is_other_month:
            # Other month: light gray
            date_button.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    color: #D1D5DB;
                    border: none;
                    border-radius: 18px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #F3F4F6;
                }
            """)
        else:
            # Current month: dark gray
            date_button.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    color: #374151;
                    border: none;
                    border-radius: 18px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #F3F4F6;
                }
            """)

        container_layout.addWidget(date_button)

        # Connect click event
        date_button.clicked.connect(lambda checked, d=btn_date: self.on_date_clicked(d))

        return date_container
    
    def on_date_clicked(self, date):
        self.selected_date = date
        self.date_clicked.emit(date)
    
    def prev_month(self):
        self.current_date = self.current_date.addMonths(-1)
        self.update_calendar()
    
    def next_month(self):
        self.current_date = self.current_date.addMonths(1)
        self.update_calendar()


class TaskListWidget(QWidget):
    """Task list widget for displaying daily tasks"""

    def __init__(self):
        super().__init__()
        self.current_date = QDate.currentDate()
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setSpacing(0)
        layout.setContentsMargins(40, 0, 0, 0)  # Left margin for spacing from divider

        # Title section
        self.title_label = QLabel()
        font = QFont()
        font.setPointSize(16)
        font.setBold(True)
        self.title_label.setFont(font)
        self.title_label.setStyleSheet("""
            QLabel {
                color: #111827;
                margin: 0;
                padding: 0;
                margin-bottom: 20px;
            }
        """)

        # Task list container
        self.task_container = QWidget()
        self.task_layout = QVBoxLayout(self.task_container)
        self.task_layout.setSpacing(2)  # Tighter spacing
        self.task_layout.setAlignment(Qt.AlignTop)
        self.task_layout.setContentsMargins(0, 0, 0, 0)

        layout.addWidget(self.title_label)
        layout.addWidget(self.task_container)
        layout.addStretch()
        self.setLayout(layout)

        self.update_tasks()
    
    def update_tasks(self):
        # Clear existing tasks
        for i in reversed(range(self.task_layout.count())):
            child = self.task_layout.itemAt(i).widget()
            if child:
                child.deleteLater()

        # Update title
        date_str = self.current_date.toString("MMMM d, yyyy")
        self.title_label.setText(f"Schedule for {date_str}")

        # Sample tasks (you can replace this with actual data)
        sample_tasks = [
            {"name": "Leslie Alexander", "time": "1:00 PM - 2:30 PM", "avatar": "LA"},
            {"name": "Michael Foster", "time": "3:00 PM - 4:30 PM", "avatar": "MF"},
            {"name": "Dries Vincent", "time": "5:00 PM - 6:30 PM", "avatar": "DV"},
            {"name": "Lindsay Walton", "time": "7:00 PM - 8:30 PM", "avatar": "LW"},
        ]

        if sample_tasks:
            for task in sample_tasks:
                task_widget = self.create_task_widget(task["name"], task["time"], task["avatar"])
                self.task_layout.addWidget(task_widget)
        else:
            # No tasks message
            no_tasks_label = QLabel("No meetings scheduled")
            no_tasks_label.setAlignment(Qt.AlignCenter)
            no_tasks_label.setStyleSheet("color: #6B7280; font-style: italic; padding: 20px;")
            self.task_layout.addWidget(no_tasks_label)
    
    def create_task_widget(self, name, time, avatar_text):
        # Main container with hover effect
        widget = QFrame()
        widget.setFixedHeight(60)
        widget.setStyleSheet("""
            QFrame {
                background-color: transparent;
                border: none;
                border-radius: 12px;
            }
            QFrame:hover {
                background-color: #F3F4F6;
            }
        """)

        # Main horizontal layout
        layout = QHBoxLayout(widget)
        layout.setSpacing(12)  # space-x-3
        layout.setContentsMargins(12, 8, 12, 8)

        # Avatar (circular with initials) - smaller and with different colors
        avatar = QLabel(avatar_text)
        avatar.setFixedSize(32, 32)  # Smaller avatar
        avatar.setAlignment(Qt.AlignCenter)

        # Different background colors for different people
        avatar_colors = {
            "LA": "#EF4444",  # Red
            "MF": "#3B82F6",  # Blue
            "DV": "#10B981",  # Green
            "LW": "#F59E0B",  # Yellow
        }
        bg_color = avatar_colors.get(avatar_text, "#6B7280")

        avatar.setStyleSheet(f"""
            QLabel {{
                background-color: {bg_color};
                color: white;
                border-radius: 16px;
                font-weight: 600;
                font-size: 12px;
            }}
        """)

        # Content area (flex-auto)
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(2)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setAlignment(Qt.AlignVCenter)

        # Name
        name_label = QLabel(name)
        name_label.setStyleSheet("""
            QLabel {
                color: #111827;
                font-size: 14px;
                font-weight: 500;
                margin: 0;
                padding: 0;
            }
        """)

        # Time
        time_label = QLabel(time)
        time_label.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 13px;
                margin: 0;
                padding: 0;
            }
        """)

        content_layout.addWidget(name_label)
        content_layout.addWidget(time_label)

        layout.addWidget(avatar)
        layout.addWidget(content_widget, 1)  # flex-auto

        return widget
    
    def set_date(self, date):
        self.current_date = date
        self.update_tasks()


class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("Calendar App")
        self.setGeometry(100, 100, 1052, 575)
        
        # Set application icon (you can add an icon file later)
        # self.setWindowIcon(QIcon("calendar_icon.png"))
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout with grid-like structure
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(32, 32, 32, 32)
        main_layout.setSpacing(0)

        # Calendar widget (left side)
        self.calendar_widget = CalendarWidget()
        self.calendar_widget.setMinimumWidth(400)

        # Vertical divider
        divider = QFrame()
        divider.setFrameShape(QFrame.VLine)
        divider.setFrameShadow(QFrame.Sunken)
        divider.setStyleSheet("""
            QFrame {
                color: #E5E7EB;
                background-color: #E5E7EB;
                border: none;
                width: 1px;
            }
        """)

        # Task list widget (right side)
        self.task_widget = TaskListWidget()
        self.task_widget.setMinimumWidth(300)

        # Add widgets to main layout
        main_layout.addWidget(self.calendar_widget, 1)
        main_layout.addWidget(divider)
        main_layout.addWidget(self.task_widget, 1)
        
        # Connect signals
        self.calendar_widget.date_clicked.connect(self.task_widget.set_date)
        
        # Set application style
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ffffff;
            }
            QWidget {
                background-color: #ffffff;
            }
        """)


def main():
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Calendar App")
    app.setApplicationVersion("1.0")
    
    # Create and show main window
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Calendar Desktop Application
A PySide6-based calendar application with task management
"""

import sys
import calendar
from datetime import datetime, date
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGridLayout, QLabel, QPushButton, QFrame, QScrollArea, QSplitter
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont, QPalette, QIcon


class CalendarWidget(QWidget):
    """Custom calendar widget"""
    date_clicked = Signal(QDate)
    
    def __init__(self):
        super().__init__()
        self.current_date = QDate.currentDate()
        self.selected_date = self.current_date
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setSpacing(10)
        
        # Header with navigation
        header_layout = QHBoxLayout()
        
        self.prev_button = QPushButton("◀")
        self.prev_button.setFixedSize(40, 40)
        self.prev_button.clicked.connect(self.prev_month)
        
        self.month_label = QLabel()
        self.month_label.setAlignment(Qt.AlignCenter)
        font = QFont()
        font.setPointSize(18)
        font.setBold(True)
        self.month_label.setFont(font)
        
        self.next_button = QPushButton("▶")
        self.next_button.setFixedSize(40, 40)
        self.next_button.clicked.connect(self.next_month)
        
        header_layout.addWidget(self.prev_button)
        header_layout.addWidget(self.month_label, 1)
        header_layout.addWidget(self.next_button)
        
        # Calendar grid
        self.calendar_frame = QFrame()
        self.calendar_layout = QGridLayout(self.calendar_frame)
        self.calendar_layout.setSpacing(1)
        
        # Week day headers
        weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        for i, day in enumerate(weekdays):
            label = QLabel(day)
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("""
                QLabel {
                    background-color: #f0f0f0;
                    border: 1px solid #ddd;
                    padding: 8px;
                    font-weight: bold;
                    color: #666;
                }
            """)
            self.calendar_layout.addWidget(label, 0, i)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.calendar_frame)
        self.setLayout(layout)
        
        self.update_calendar()
        
    def update_calendar(self):
        # Clear existing date buttons
        for i in range(1, 7):  # rows 1-6 (row 0 is weekday headers)
            for j in range(7):  # columns 0-6
                item = self.calendar_layout.itemAtPosition(i, j)
                if item:
                    widget = item.widget()
                    if widget:
                        widget.deleteLater()
        
        # Update month label
        month_name = self.current_date.toString("MMMM yyyy")
        self.month_label.setText(month_name)
        
        # Get calendar data
        year = self.current_date.year()
        month = self.current_date.month()
        
        # Get first day of month and number of days
        first_day = QDate(year, month, 1)
        days_in_month = first_day.daysInMonth()
        
        # Get the weekday of the first day (0=Monday, 6=Sunday)
        first_weekday = first_day.dayOfWeek() - 1
        
        # Calculate previous month days to show
        prev_month_date = first_day.addMonths(-1)
        prev_month_days = prev_month_date.daysInMonth()
        
        row = 1
        col = 0
        
        # Add previous month's trailing days
        for i in range(first_weekday):
            day = prev_month_days - first_weekday + i + 1
            btn = self.create_date_button(day, True, prev_month_date.year(), prev_month_date.month())
            self.calendar_layout.addWidget(btn, row, col)
            col += 1
        
        # Add current month days
        for day in range(1, days_in_month + 1):
            btn = self.create_date_button(day, False, year, month)
            self.calendar_layout.addWidget(btn, row, col)
            col += 1
            if col > 6:
                col = 0
                row += 1
        
        # Add next month's leading days
        next_month_date = first_day.addMonths(1)
        day = 1
        while row < 7:
            while col < 7:
                btn = self.create_date_button(day, True, next_month_date.year(), next_month_date.month())
                self.calendar_layout.addWidget(btn, row, col)
                day += 1
                col += 1
            col = 0
            row += 1
            if row >= 7:
                break
    
    def create_date_button(self, day, is_other_month, year, month):
        btn = QPushButton(str(day))
        btn.setFixedSize(50, 50)
        
        # Create QDate for this button
        btn_date = QDate(year, month, day)
        
        # Style the button
        if is_other_month:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: white;
                    border: 1px solid #ddd;
                    color: #ccc;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #f5f5f5;
                }
            """)
        else:
            # Check if this is today
            if btn_date == QDate.currentDate():
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: white;
                        border: 2px solid #ff6b35;
                        color: #ff6b35;
                        font-size: 14px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #fff5f2;
                    }
                """)
            else:
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: white;
                        border: 1px solid #ddd;
                        color: #333;
                        font-size: 14px;
                    }
                    QPushButton:hover {
                        background-color: #f5f5f5;
                    }
                    QPushButton:pressed {
                        background-color: #e0e0e0;
                    }
                """)
        
        # Connect click event
        btn.clicked.connect(lambda checked, d=btn_date: self.on_date_clicked(d))
        
        return btn
    
    def on_date_clicked(self, date):
        self.selected_date = date
        self.date_clicked.emit(date)
    
    def prev_month(self):
        self.current_date = self.current_date.addMonths(-1)
        self.update_calendar()
    
    def next_month(self):
        self.current_date = self.current_date.addMonths(1)
        self.update_calendar()


class TaskListWidget(QWidget):
    """Task list widget for displaying daily tasks"""
    
    def __init__(self):
        super().__init__()
        self.current_date = QDate.currentDate()
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setSpacing(15)
        
        # Title
        self.title_label = QLabel()
        font = QFont()
        font.setPointSize(16)
        font.setBold(True)
        self.title_label.setFont(font)
        self.title_label.setStyleSheet("color: #333; margin-bottom: 10px;")
        
        # Scroll area for tasks
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        
        self.task_container = QWidget()
        self.task_layout = QVBoxLayout(self.task_container)
        self.task_layout.setSpacing(10)
        self.task_layout.setAlignment(Qt.AlignTop)
        
        scroll_area.setWidget(self.task_container)
        
        layout.addWidget(self.title_label)
        layout.addWidget(scroll_area)
        self.setLayout(layout)
        
        self.update_tasks()
    
    def update_tasks(self):
        # Clear existing tasks
        for i in reversed(range(self.task_layout.count())):
            child = self.task_layout.itemAt(i).widget()
            if child:
                child.deleteLater()
        
        # Update title
        date_str = self.current_date.toString("MMMM d, yyyy")
        self.title_label.setText(f"Tasks {date_str}")
        
        # Sample tasks (you can replace this with actual data)
        sample_tasks = [
            {"title": "Task 1", "time": "1:00 PM - 2:30 PM"},
            {"title": "Task 2", "time": "3:00 PM - 4:30 PM"},
            {"title": "Task 3", "time": "5:00 PM - 6:30 PM"},
            {"title": "Task 4", "time": "7:00 PM - 8:30 PM"},
        ]
        
        if sample_tasks:
            for task in sample_tasks:
                task_widget = self.create_task_widget(task["title"], task["time"])
                self.task_layout.addWidget(task_widget)
        else:
            # No tasks message
            no_tasks_label = QLabel("No tasks for this day")
            no_tasks_label.setAlignment(Qt.AlignCenter)
            no_tasks_label.setStyleSheet("color: #999; font-style: italic; padding: 20px;")
            self.task_layout.addWidget(no_tasks_label)
    
    def create_task_widget(self, title, time):
        widget = QFrame()
        widget.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px;
                margin: 2px;
            }
            QFrame:hover {
                border-color: #ff6b35;
                background-color: #fff9f7;
            }
        """)
        
        layout = QVBoxLayout(widget)
        layout.setSpacing(5)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # Task title
        title_label = QLabel(title)
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #333;")
        
        # Task time
        time_label = QLabel(time)
        time_label.setStyleSheet("color: #666; font-size: 11px;")
        
        layout.addWidget(title_label)
        layout.addWidget(time_label)
        
        return widget
    
    def set_date(self, date):
        self.current_date = date
        self.update_tasks()


class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("Calendar App")
        self.setGeometry(100, 100, 1200, 800)
        
        # Set application icon (you can add an icon file later)
        # self.setWindowIcon(QIcon("calendar_icon.png"))
        
        # Central widget with splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Horizontal)
        
        # Calendar widget
        self.calendar_widget = CalendarWidget()
        self.calendar_widget.setMinimumWidth(600)
        
        # Task list widget
        self.task_widget = TaskListWidget()
        self.task_widget.setMinimumWidth(300)
        
        # Add widgets to splitter
        splitter.addWidget(self.calendar_widget)
        splitter.addWidget(self.task_widget)
        
        # Set initial splitter sizes (60% calendar, 40% tasks)
        splitter.setSizes([720, 480])
        
        main_layout.addWidget(splitter)
        
        # Connect signals
        self.calendar_widget.date_clicked.connect(self.task_widget.set_date)
        
        # Set application style
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QWidget {
                background-color: #f8f9fa;
            }
        """)


def main():
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Calendar App")
    app.setApplicationVersion("1.0")
    
    # Create and show main window
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()

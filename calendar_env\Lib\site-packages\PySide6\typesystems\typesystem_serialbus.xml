<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->

<typesystem package="PySide6.QtSerialBus"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
    <load-typesystem name="typesystem_core.xml" generate="no"/>
    <load-typesystem name="typesystem_network.xml" generate="no"/>
    <load-typesystem name="typesystem_serialport.xml" generate="no"/>

    <namespace-type name="QtCanBus">
        <enum-type name="DataSource"/>
        <enum-type name="DataFormat"/>
        <enum-type name="MultiplexState"/>
        <enum-type name="UniqueId"/>
    </namespace-type>

    <object-type name="QCanBus">
        <!-- Remove errorMessage argument, return tuple instead.  -->
        <modify-function signature="availableDevices(QString*)const">
            <modify-argument index="1">
                <remove-default-expression/>
                <remove-argument/>
            </modify-argument>
            <modify-argument index="return">
                <replace-type modified-type="tuple"/>
            </modify-argument>
            <inject-code class="target" position="beginning"
                         file="../glue/qtserialbus.cpp" snippet="qcanbus-available-devices"/>
            <inject-documentation format="target" mode="append">
            The function returns a tuple of (device_list, error_string).
            </inject-documentation>
        </modify-function>
        <!-- Remove errorMessage argument, return tuple instead.  -->
        <modify-function signature="availableDevices(QString,QString*)const">
            <modify-argument index="2">
                <remove-default-expression/>
                <remove-argument/>
            </modify-argument>
            <modify-argument index="return">
                <replace-type modified-type="tuple"/>
            </modify-argument>
            <inject-code class="target" position="beginning"
                         file="../glue/qtserialbus.cpp" snippet="qcanbus-available-devices-plugin"/>
            <inject-documentation format="target" mode="append" emphasis="language-note">
            The function returns a tuple of (device_list, error_string).
            </inject-documentation>
        </modify-function>

        <modify-function signature="createDevice(QString,QString,QString*)const">
            <modify-argument index="3">
                <remove-default-expression/>
                <remove-argument/>
            </modify-argument>
            <modify-argument index="return"> <!-- Suppress return value heuristics -->
                <define-ownership class="target" owner="default"/>
                <replace-type modified-type="tuple"/>
            </modify-argument>
            <inject-code class="target" position="beginning"
                         file="../glue/qtserialbus.cpp" snippet="qcanbus-createdevice"/>
            <inject-documentation format="target" mode="append" emphasis="language-note">
            The function returns a tuple of (device, error_string).
            </inject-documentation>
        </modify-function>
    </object-type>
    <object-type name="QCanBusDevice">
        <enum-type name="CanBusError"/>
        <enum-type name="CanBusDeviceState"/>
        <enum-type name="CanBusStatus"/>
        <enum-type name="ConfigurationKey"/>
        <enum-type name="Direction" flags="Directions"/>
        <modify-function signature="deviceInfo()const" access="final"/>
        <value-type name="Filter">
            <enum-type name="FormatFilter" flags="FormatFilters"/>
        </value-type>
    </object-type>
    <value-type name="QCanBusDeviceInfo"> <!-- deleted default constructor -->
        <modify-function signature="swap(QCanBusDeviceInfo&amp;)" remove="all"/>
    </value-type>
    <object-type name="QCanBusFactory"/>
    <value-type name="QCanBusFrame">
        <enum-type name="FrameType"/>
        <enum-type name="FrameError" flags="FrameErrors"/>
        <value-type name="TimeStamp"/>
    </value-type>
    <object-type name="QCanDbcFileParser">
        <modify-function signature="parse(const QString&amp;)" overload-number="0"/>
        <modify-function signature="parse(const QStringList&amp;)" overload-number="1"/>
        <enum-type name="Error"/>
    </object-type>
    <object-type name="QCanFrameProcessor">
        <enum-type name="Error"/>
        <value-type name="ParseResult"/>
    </object-type>
    <value-type name="QCanMessageDescription"/>
    <value-type name="QCanSignalDescription">
        <value-type name="MultiplexValueRange"/>
    </value-type>
    <value-type name="QCanUniqueIdDescription"/>
    <object-type name="QModbusClient">
        <modify-function signature="sendReadRequest(QModbusDataUnit,int)">
            <modify-argument index="0"> <!-- Suppress return value heuristics -->
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="sendWriteRequest(QModbusDataUnit,int)">
            <modify-argument index="0"> <!-- Suppress return value heuristics -->
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="sendReadWriteRequest(QModbusDataUnit,QModbusDataUnit,int)">
            <modify-argument index="0"> <!-- Suppress return value heuristics -->
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="sendRawRequest(QModbusRequest,int)">
            <modify-argument index="0"> <!-- Suppress return value heuristics -->
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
    </object-type>
    <value-type name="QModbusDataUnit">
        <enum-type name="RegisterType"/>
    </value-type>
    <object-type name="QModbusDevice">
        <enum-type name="Error"/>
        <enum-type name="State"/>
        <enum-type name="ConnectionParameter"/>
        <enum-type name="IntermediateError"/>
    </object-type>
    <value-type name="QModbusDeviceIdentification">
        <enum-type name="ObjectId"/>
        <enum-type name="ReadDeviceIdCode"/>
        <enum-type name="ConformityLevel"/>
    </value-type>
    <object-type name="QModbusPdu">
        <enum-type name="ExceptionCode"/>
        <enum-type name="FunctionCode"/>
        <modify-field name="ExceptionByte" remove="true"/> <!-- Link error -->
    </object-type>
    <object-type name="QModbusExceptionResponse"/>
    <object-type name="QModbusResponse"/>
    <object-type name="QModbusReply">
        <enum-type name="ReplyType"/>
    </object-type>
    <object-type name="QModbusRequest"/>
    <object-type name="QModbusRtuSerialClient"/>
    <object-type name="QModbusRtuSerialServer"/>
    <object-type name="QModbusServer">
        <enum-type name="Option"/>
    </object-type>
    <object-type name="QModbusTcpClient"/>
    <object-type name="QModbusTcpConnectionObserver"/>
    <object-type name="QModbusTcpServer"/>
</typesystem>

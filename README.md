# Calendar Desktop Application

一个基于 PySide6 的现代桌面日历应用程序，具有任务管理功能。

## 功能特性

### 📅 日历功能
- **月视图显示**：完整的月份日历网格
- **今日高亮**：当前日期用橙色标记
- **月份导航**：使用左右箭头切换月份
- **跨月显示**：显示上月末尾和下月开始的日期
- **交互式日期**：点击日期查看对应任务

### 📋 任务管理
- **任务列表**：右侧显示选中日期的任务
- **任务详情**：显示任务标题和时间范围
- **动态更新**：点击不同日期自动切换任务列表
- **美观界面**：现代化的任务卡片设计

### 🎨 界面设计
- **响应式布局**：窗口可调整大小，左右面板按比例缩放
- **现代风格**：简洁清爽的界面设计
- **浅色主题**：默认浅色配色方案
- **悬停效果**：鼠标悬停时的视觉反馈

## 安装和运行

### 系统要求
- Python 3.8+
- Windows 操作系统
- PySide6

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用程序
```bash
python main.py
```

或者使用运行脚本：
```bash
python run.py
```

## 界面说明

### 主界面布局
- **左侧**：日历视图 (60% 宽度)
- **右侧**：任务列表 (40% 宽度)
- **默认尺寸**：1200 x 800 像素

### 日历操作
1. **查看不同月份**：点击左右箭头按钮
2. **选择日期**：点击日历中的任何日期
3. **今日标识**：当前日期显示为橙色边框

### 任务查看
1. **查看任务**：点击日历中的日期，右侧会显示该日期的任务
2. **任务信息**：每个任务显示标题和时间范围
3. **滚动查看**：任务较多时可以滚动查看

## 技术实现

### 核心组件
- `CalendarWidget`：自定义日历组件
- `TaskListWidget`：任务列表组件
- `MainWindow`：主窗口容器

### 关键特性
- **信号槽机制**：日历和任务列表之间的通信
- **自定义样式**：CSS 样式表定制界面外观
- **响应式设计**：QSplitter 实现可调整的面板布局
- **日期处理**：QDate 类处理日期逻辑

## 文件结构
```
calendar5.0/
├── main.py              # 主应用程序文件
├── run.py               # 运行脚本
├── requirements.txt     # 依赖列表
└── README.md           # 说明文档
```

## 开发说明

### 扩展功能
当前版本是 UI 原型，后续可以添加：
- 任务的增删改功能
- 数据持久化存储
- 深色主题切换
- 任务提醒功能
- 导入导出功能

### 自定义样式
应用程序使用 Qt 样式表 (QSS) 进行界面美化，可以通过修改 `setStyleSheet()` 中的 CSS 来调整外观。

## 许可证
MIT License

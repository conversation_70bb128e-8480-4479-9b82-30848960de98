<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtSerialPort"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
    <load-typesystem name="typesystem_core.xml" generate="no"/>
    <object-type name="QSerialPort">
        <enum-type name="BaudRate" python-type="IntEnum"/>
        <enum-type name="DataBits"/>
        <enum-type name="Direction" flags="Directions"/>
        <enum-type name="FlowControl"/>
        <enum-type name="Parity"/>
        <enum-type name="PinoutSignal" flags="PinoutSignals"/>
        <enum-type name="SerialPortError"/>
        <enum-type name="StopBits"/>
    </object-type>
    <value-type name="QSerialPortInfo"/>
</typesystem>
